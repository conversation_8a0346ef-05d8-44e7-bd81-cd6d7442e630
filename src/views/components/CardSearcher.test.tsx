import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import { ComponentProps } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createArray } from '../../../tests/fake/Fake';
import { createFakeSuggestedCard } from '../../../tests/fake/FakeCardData';
import { FakeCardSet } from '../../../tests/fake/FakeCardSet';
import { renderWithDispatcher } from '../../../tests/test-utils';
import { typeInCardSearcher } from '../../../tests/views/components/cardSearcher/helpers';
import { Game } from '../../models/Game';
import CardSearcher from './CardSearcher';

// Setup mocks and test data outside of the test cases
const mockChooseSuggestion = vi.fn();
const mockUpdateSetFilters = vi.fn();

const mockCards = createFakeSuggestedCard(10);

const mockSets = createArray(FakeCardSet, 10);
const mockAbort = vi.fn();
const mockSearchCards = vi.fn().mockImplementation((_query: string) => ({
  request: { abort: mockAbort },
  promise: Promise.resolve(Immutable.List(mockCards)),
}));
const mockSearchSets = vi.fn().mockImplementation((_query: string) => ({
  request: { abort: vi.fn() },
  promise: Promise.resolve(Immutable.List(mockSets)),
}));

const renderCardSearcher = (props: Partial<ComponentProps<typeof CardSearcher>> = {}) => {
  return renderWithDispatcher(CardSearcher, {
    chooseSuggestion: mockChooseSuggestion,
    searchCards: mockSearchCards,
    searchSets: mockSearchSets,
    updateSetFilters: mockUpdateSetFilters,
    game: Game.MTG,
    ...props,
  });
};

describe('CardSearcher', () => {
  beforeEach(async () => {
    // @handleCardSearch in CardSearcher uses setTimeout(200ms) for API request debouncing.
    // This creates a race condition in tests because:
    // 1. Previous test -> setTimeout -> Node API Queue
    // 2. After 200ms -> Callback Queue -> Call Stack
    // Without this wait, mocks would be cleared too early and next test would see previous test's values
    await new Promise((resolve) => setTimeout(resolve, 200));
  });

  describe('component render', () => {
    it('renders the search input', () => {
      renderCardSearcher();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('displays the default placeholder text', () => {
      renderCardSearcher();
      expect(screen.getByText(/Search cards by name/i)).toBeInTheDocument();
    });
  });

  describe('when searching', () => {
    it('only triggers search with 3 or more characters', async () => {
      renderCardSearcher();

      // Type a short query (less than 3 chars)
      const shortQuery = mockCards[0].get('name').slice(0, 2);
      typeInCardSearcher(shortQuery);

      // Now type a query with at least 3 chars
      const longQuery = mockCards[0].get('name').slice(0, 3);
      typeInCardSearcher(longQuery);

      // Wait for the search to be triggered
      await waitFor(() => {
        expect(mockSearchCards).toHaveBeenCalledExactlyOnceWith(longQuery);
      });
    });

    it('shows searching state indicator', () => {
      renderCardSearcher();
      typeInCardSearcher('Test Search');
      expect(screen.getByText('Searching...')).toBeInTheDocument();
    });

    describe('with debouncing', () => {
      beforeEach(() => {
        vi.clearAllMocks();
        vi.useFakeTimers();
      });

      afterEach(() => {
        vi.runOnlyPendingTimers();
        vi.useRealTimers();
      });

      it('search cards with 200ms delay', async () => {
        renderCardSearcher();

        const searchTerm = 'test search';
        typeInCardSearcher(searchTerm);

        // Search should not be called immediately
        expect(mockSearchCards).not.toHaveBeenCalled();

        // Fast-forward time by 199ms - should still not be called
        vi.advanceTimersByTime(199);
        expect(mockSearchCards).not.toHaveBeenCalled();

        // Fast-forward by 1ms more (total 200ms) - now it should be called
        vi.advanceTimersByTime(1);
        expect(mockSearchCards).toHaveBeenCalledWith(searchTerm);
      });

      it('search sets with 200ms delay', async () => {
        renderCardSearcher();

        const setSearchTerm = 's:test set';
        typeInCardSearcher(setSearchTerm);

        // Search should not be called immediately
        expect(mockSearchSets).not.toHaveBeenCalled();

        // Fast-forward time by 199ms - should still not be called
        vi.advanceTimersByTime(199);
        expect(mockSearchSets).not.toHaveBeenCalled();

        // Fast-forward by 1ms more (total 200ms) - now it should be called
        vi.advanceTimersByTime(1);
        expect(mockSearchSets).toHaveBeenCalledWith('test set');
      });

      it('cancels previous search when typing rapidly', async () => {
        renderCardSearcher();

        // Type first search term
        typeInCardSearcher('first');

        // Fast-forward by 100ms (not enough to trigger search)
        vi.advanceTimersByTime(100);
        expect(mockSearchCards).not.toHaveBeenCalled();

        // Type second search term before first one completes
        typeInCardSearcher('second');

        // Fast-forward by 200ms - only the second search should be called
        vi.advanceTimersByTime(200);
        expect(mockSearchCards).toHaveBeenCalledExactlyOnceWith('second');
        expect(mockSearchCards).not.toHaveBeenCalledWith('first');
      });

      it('aborts previous request when new search is initiated', async () => {
        renderCardSearcher({ searchCards: mockSearchCards });

        // First search
        typeInCardSearcher('first');
        vi.advanceTimersByTime(200);
        expect(mockSearchCards).toHaveBeenCalledWith('first');

        // Second search should abort the first
        typeInCardSearcher('second');
        expect(mockAbort).toHaveBeenCalled();

        vi.advanceTimersByTime(200);
        expect(mockSearchCards).toHaveBeenCalledWith('second');
      });
    });

    describe('no results', () => {
      it('displays correct message', async () => {
        const emptyMockSearchCards = vi.fn().mockImplementationOnce((_query: string) => ({
          request: { abort: vi.fn() },
          promise: Promise.resolve(Immutable.List([])),
        }));

        renderCardSearcher({ searchCards: emptyMockSearchCards });
        const term = 'non-existent-card';
        typeInCardSearcher(term);

        await waitFor(() => {
          expect(emptyMockSearchCards).toHaveBeenCalledWith(term);
          expect(screen.getByText('No Results')).toBeInTheDocument();
        });
      });
    });

    ['card', 'set'].forEach((type) => {
      const searchFunction = type === 'card' ? 'searchCards' : 'searchSets';
      const searchTerm = type === 'card' ? 'failing search' : 's:failing search';
      describe(`${type} fails`, () => {
        beforeEach(() => {
          vi.spyOn(console, 'error').mockImplementation(() => {});
        });

        it('shows no result', async () => {
          const searchError = new Error('Network error');
          const failingMockSearch = vi.fn().mockImplementation((_query: string) => ({
            request: { abort: vi.fn() },
            promise: Promise.reject(searchError),
          }));

          renderCardSearcher({ [searchFunction]: failingMockSearch });

          typeInCardSearcher(searchTerm);

          // Wait for the search function to be called
          await waitFor(() => {
            expect(failingMockSearch).toHaveBeenCalledWith('failing search');
          });

          // Wait for the promise rejection to be handled and console.error to be called
          await waitFor(() => {
            expect(console.error).toHaveBeenCalledWith(searchError);
          });

          // Should not show loading state after error
          expect(screen.queryByText('Searching...')).not.toBeInTheDocument();

          // Should show no results message since options is undefined
          expect(screen.getByText('No Results')).toBeInTheDocument();
        });
      });
    });
  });

  describe('with CardSuggestion', () => {
    it('selecting a card suggestion', async () => {
      const { container } = renderCardSearcher();

      const searchTerm = mockCards[0].get('name').slice(0, 3);
      typeInCardSearcher(searchTerm);

      // Wait for debounced search to complete and results to be displayed
      await waitFor(
        () => {
          expect(mockSearchCards).toHaveBeenCalledWith(searchTerm);
        },
        { timeout: 1000 },
      );

      // Wait for the options to be rendered
      await waitFor(
        () => {
          const suggestion = container.querySelector('.react-select-cards__option');
          expect(suggestion).not.toBeNull();
        },
        { timeout: 1000 },
      );

      const suggestion = container.querySelector('.react-select-cards__option');
      if (suggestion) {
        fireEvent.click(suggestion);
        expect(mockChooseSuggestion).toHaveBeenCalledWith(mockCards[0]);
      }
    });
  });

  describe('with CardSetFilterSuggestion', () => {
    it('using SearchSets when "s:" prefix is typed', async () => {
      renderCardSearcher();

      const setName = mockSets[0].get('name');
      const setFilterQuery = `s:${setName}`;
      typeInCardSearcher(setFilterQuery);

      await waitFor(() => {
        expect(mockSearchSets).toHaveBeenCalledWith(setName);
      });
    });

    it('selecting set suggestions', async () => {
      const { container } = renderCardSearcher();

      const setName = mockSets[0].get('name');
      const setFilterQuery = `s:${setName}`;
      typeInCardSearcher(setFilterQuery);

      await waitFor(() => {
        expect(mockSearchSets).toHaveBeenCalledWith(setName);
      });
      const setSuggestion = container.querySelector('.react-select-cards__option');
      expect(setSuggestion).not.toBeNull();

      if (setSuggestion) {
        fireEvent.click(setSuggestion);
        expect(mockUpdateSetFilters).toHaveBeenCalled();
      }
    });
  });

  describe('with CardSearcherImagePreview', () => {
    it('displays preview image ', async () => {
      renderCardSearcher();

      const searchTerm = mockCards[0].get('name').slice(0, 3);
      typeInCardSearcher(searchTerm);

      await waitFor(() => {
        expect(
          screen.getByRole('img', {
            name: mockCards[0].get('name'),
          }),
        ).toBeInTheDocument();
      });
    });
  });

  describe('onChangeQuery method edge cases', () => {
    it('handles negative set filter search with -s: prefix', async () => {
      renderCardSearcher();

      const setName = mockSets[0].get('name');
      const negativeSetQuery = `-s:${setName}`;
      typeInCardSearcher(negativeSetQuery);

      await waitFor(() => {
        expect(mockSearchSets).toHaveBeenCalledWith(setName);
      });
    });

    it('clears chosen card and calls chooseSuggestion when chosenCard exists', async () => {
      const { container } = renderCardSearcher();

      // First, simulate selecting a card to set chosenCard state
      const searchTerm = mockCards[0].get('name').slice(0, 3);
      typeInCardSearcher(searchTerm);

      // Wait for search results
      await waitFor(() => {
        expect(mockSearchCards).toHaveBeenCalledWith(searchTerm);
      });

      // Wait for options to be rendered and select one
      await waitFor(() => {
        const suggestion = container.querySelector('.react-select-cards__option');
        expect(suggestion).not.toBeNull();
      });

      const suggestion = container.querySelector('.react-select-cards__option');
      if (suggestion) {
        fireEvent.click(suggestion);
        expect(mockChooseSuggestion).toHaveBeenCalledWith(mockCards[0]);
      }

      // Clear mocks to test the next behavior
      vi.clearAllMocks();

      // Now type something new - this should trigger the chosenCard branch
      typeInCardSearcher('new search');

      // Should call chooseSuggestion without arguments (clearing selection)
      expect(mockChooseSuggestion).toHaveBeenCalledWith();
    });
  });
});
